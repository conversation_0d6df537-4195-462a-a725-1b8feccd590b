"use client";
import { useState, useEffect } from "react";
import { SolanaConnect } from "../components/SolanaConnect";
import EthereumConnect from "@/components/EthereumConnect";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true); // Set to true when component mounts (client-side)
  }, []);

  if (!isClient) return null; // Prevent rendering mismatched content

  return (
    <Providers>
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
            LayerZero Bridge - Mainnet
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <SolanaConnect />
            <EthereumConnect />
          </div>

          <div className="flex justify-center">
            <BridgeInterface />
          </div>
        </div>
      </div>
    </Providers>
  );
}
