"use client";
import { useWallet } from "@solana/wallet-adapter-react";
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from "wagmi";
import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { useState, useEffect, useCallback, useMemo } from "react";
import { EndpointId } from "@layerzerolabs/lz-definitions";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { LAMPORTS_PER_SOL, Connection, PublicKey } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { fromWeb3JsPublicKey } from '@metaplex-foundation/umi-web3js-adapters';
import { findAssociatedTokenPda, createSplAssociatedTokenProgram, setComputeUnitLimit, setComputeUnitPrice } from '@metaplex-foundation/mpl-toolbox';
import bs58 from 'bs58';
import TransactionStatus from './TransactionStatus';
import { useReadContract } from 'wagmi';
import { erc20Abi, parseUnits } from 'viem';

// Environment variables
const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");

// Mainnet endpoint IDs
const ETHEREUM_MAINNET_EID = EndpointId.ETHEREUM_V2_MAINNET;
const SOLANA_MAINNET_EID = EndpointId.SOLANA_V2_MAINNET;

// Token decimals
const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

// LayerZero scan link utility
const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};

// OFT ABI for LayerZero OFT contracts
const oftAbi = [
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      { "internalType": "bool", "name": "_payInLzToken", "type": "bool" }
    ],
    "name": "quoteSend",
    "outputs": [
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "msgFee",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "_fee",
        "type": "tuple"
      },
      { "internalType": "address", "name": "_refundAddress", "type": "address" }
    ],
    "name": "send",
    "outputs": [
      {
        "components": [
          { "internalType": "bytes32", "name": "guid", "type": "bytes32" },
          { "internalType": "uint64", "name": "nonce", "type": "uint64" },
          {
            "components": [
              { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
              { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
            ],
            "internalType": "struct MessagingFee",
            "name": "fee",
            "type": "tuple"
          }
        ],
        "internalType": "struct MessagingReceipt",
        "name": "msgReceipt",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "amountSentLD", "type": "uint256" },
          { "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256" }
        ],
        "internalType": "struct OFTReceipt",
        "name": "oftReceipt",
        "type": "tuple"
      }
    ],
    "stateMutability": "payable",
    "type": "function"
  }
] as const;

interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
}

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

export default function BridgeInterface() {
  const solanaWallet = useWallet();
  const { address: ethAddress, isConnected: isEthConnected } = useAccount();

  const [isClient, setIsClient] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [direction, setDirection] = useState<'sol-to-eth' | 'eth-to-sol'>('sol-to-eth');
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // Memoize UMI instance to prevent recreation on every render
  const umi = useMemo(() => {
    const umiInstance = createUmi(SOLANA_RPC_URL);
    if (solanaWallet.wallet) {
      umiInstance.use(walletAdapterIdentity(solanaWallet));
    }
    // Register the SPL Associated Token program
    umiInstance.programs.add(createSplAssociatedTokenProgram());
    return umiInstance;
  }, [solanaWallet]);

  // Fetch Ethereum token balance - only when connected
  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  // Wagmi hooks for Ethereum contract interactions
  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending } = useWriteContract();
  const { isLoading: isEthTxConfirming, isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  // Quote Ethereum OFT contract
  const { data: ethQuoteData, refetch: refetchEthQuote } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: oftAbi,
    functionName: 'quoteSend',
    args: [
      {
        dstEid: SOLANA_MAINNET_EID,
        to: '0x0000000000000000000000000000000000000000000000000000000000000000', // Will be set dynamically
        amountLD: BigInt(0), // Will be set dynamically
        minAmountLD: BigInt(0), // Will be set dynamically
        extraOptions: '0x',
        composeMsg: '0x',
        oftCmd: '0x',
      },
      false // payInLzToken
    ],
    query: {
      enabled: false, // We'll trigger this manually
    },
  });

  // Memoized Solana balance fetcher
  const fetchSolanaBalance = useCallback(async () => {
    if (!solanaWallet.publicKey || !SOLANA_OFT_MINT_ADDRESS || !solanaWallet.connected) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(solanaWallet.publicKey.toString());

      // Get the associated token address
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      // Get the token account info
      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey, solanaWallet.connected]);

  // Client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Update Solana balance when wallet connects/disconnects
  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      // Clear balance when wallet disconnects
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  // Update Ethereum balance when data changes
  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      // Clear balance when wallet disconnects
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  // All callback and memoized functions - moved before conditional return
  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    if (direction === 'sol-to-eth') {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      if (!ethAddress) {
        throw new Error("Please connect your Ethereum wallet to receive tokens.");
      }
    } else {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      if (!solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet to receive tokens.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }
  }, [direction, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount]);

  const quoteSolanaToEthereum = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));

      const recipientAddressBytes32 = addressToBytes32(ethAddress!);

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey!),
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
        }
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Solana has 6 decimals, Ethereum has 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, ethAddress, solanaWallet.publicKey, umi]);

  const executeSolanaToEthereum = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteSolanaToEthereum();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));
      const recipientAddressBytes32 = addressToBytes32(ethAddress!);

      // Get token account
      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(SOLANA_OFT_MINT_ADDRESS!)),
        owner: publicKey(solanaWallet.publicKey!),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: bridgeState.nativeFee,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Add compute unit instructions to handle the complex LayerZero transaction
      const computeUnitLimitIx = setComputeUnitLimit(umi, { units: 400_000 });
      const computeUnitPriceIx = setComputeUnitPrice(umi, { microLamports: 1000 });

      const txB = transactionBuilder()
        .add(computeUnitLimitIx)
        .add(computeUnitPriceIx)
        .add([ix]);
      const { signature } = await txB.sendAndConfirm(umi);
      const txHash = bs58.encode(signature);
      const layerZeroScanLink = getLayerZeroScanLink(txHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: txHash,
        timestamp: Date.now(),
        fromChain: 'solana',
        toChain: 'ethereum',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Refresh balance after successful transaction
      if (solanaWallet.connected && solanaWallet.publicKey) {
        const newBalance = await fetchSolanaBalance();
        setBridgeState(prev => ({ ...prev, solanaBalance: newBalance }));
      }
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteSolanaToEthereum, amount, ethAddress, solanaWallet.publicKey, umi, fetchSolanaBalance, solanaWallet.connected]);

  const quoteEthereumToSolana = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Convert Solana wallet address to bytes32
      const solanaAddressBytes = bs58.decode(solanaWallet.publicKey!.toString());
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      // For now, we'll use a simulated quote since dynamic contract calls are complex
      // In a production app, you'd want to use a separate contract read with the dynamic parameters
      const simulatedNativeFee = BigInt('50000000000000000'); // 0.05 ETH as example
      const nativeFee = simulatedNativeFee;

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Ethereum has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, solanaWallet.publicKey, refetchEthQuote]);

  const executeEthereumToSolana = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteEthereumToSolana();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // This would need to be implemented with wagmi useWriteContract
      // For now, we'll simulate the transaction
      const simulatedTxHash = '0x' + Array.from({ length: 64 }, () => Math.floor(Math.random() * 16).toString(16)).join('');
      const layerZeroScanLink = getLayerZeroScanLink(simulatedTxHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: simulatedTxHash,
        timestamp: Date.now(),
        fromChain: 'ethereum',
        toChain: 'solana',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash: simulatedTxHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Refresh balance after successful transaction
      if (isEthConnected && ethAddress) {
        // Balance will be refreshed automatically by the useReadContract hook
      }
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteEthereumToSolana, amount, solanaWallet.publicKey, isEthConnected, ethAddress]);

  const canQuote = useMemo(() => {
    return direction === 'sol-to-eth'
      ? solanaWallet.connected && ethAddress
      : isEthConnected && solanaWallet.publicKey;
  }, [direction, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey]);

  const canExecute = useMemo(() => {
    return canQuote && bridgeState.nativeFee !== null;
  }, [canQuote, bridgeState.nativeFee]);

  const clearTransactionHistory = useCallback(() => {
    setTransactions([]);
  }, []);

  const handleSolToEthClick = useCallback(() => {
    setDirection('sol-to-eth');
    resetBridgeState();
  }, [resetBridgeState]);

  const handleEthToSolClick = useCallback(() => {
    setDirection('eth-to-sol');
    resetBridgeState();
  }, [resetBridgeState]);

  const handleAmountChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setAmount(e.target.value);
    resetBridgeState();
  }, [resetBridgeState]);

  if (!isClient) return null;

  return (
    <div className="max-w-md mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-center mb-6">LayerZero Bridge</h2>

        {/* Direction Toggle */}
        <div className="flex mb-4 bg-gray-100 rounded-lg p-1">
          <button
            onClick={handleSolToEthClick}
            className={`flex-1 py-2 px-4 rounded-md transition-colors ${direction === 'sol-to-eth'
              ? 'bg-blue-500 text-white'
              : 'text-gray-600 hover:bg-gray-200'
              }`}
          >
            Solana → Ethereum
          </button>
          <button
            onClick={handleEthToSolClick}
            className={`flex-1 py-2 px-4 rounded-md transition-colors ${direction === 'eth-to-sol'
              ? 'bg-blue-500 text-white'
              : 'text-gray-600 hover:bg-gray-200'
              }`}
          >
            Ethereum → Solana
          </button>
        </div>

        {/* Amount Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Amount (Cryptocurrency Coin)
          </label>
          <input
            type="number"
            value={amount}
            onChange={handleAmountChange}
            step="0.000001"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0.1"
          />

          {/* Balance Display */}
          <div className="mt-2 text-sm text-gray-600">
            {direction === 'sol-to-eth' ? (
              <div className="flex justify-between">
                <span>Available on Solana:</span>
                <span className="font-medium">
                  {bridgeState.solanaBalance ? `${bridgeState.solanaBalance} CRYPTO` : 'Loading...'}
                </span>
              </div>
            ) : (
              <div className="flex justify-between">
                <span>Available on Ethereum:</span>
                <span className="font-medium">
                  {bridgeState.ethereumBalance ? `${bridgeState.ethereumBalance} CRYPTO` : 'Loading...'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Connection Status */}
        <div className="mb-4 text-sm">
          <div className="flex justify-between items-center mb-2">
            <span>Solana:</span>
            <span className={solanaWallet.connected ? 'text-green-600' : 'text-red-600'}>
              {solanaWallet.connected ? 'Connected' : 'Not Connected'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span>Ethereum:</span>
            <span className={isEthConnected ? 'text-green-600' : 'text-red-600'}>
              {isEthConnected ? 'Connected' : 'Not Connected'}
            </span>
          </div>
        </div>

        {/* Quote Display */}
        {bridgeState.nativeFee && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg space-y-2">
            <div className="flex justify-between text-sm text-blue-800">
              <span>Bridge Fee:</span>
              <span className="font-medium">
                {(Number(bridgeState.nativeFee) / LAMPORTS_PER_SOL).toFixed(6)} SOL
              </span>
            </div>
            {bridgeState.receiveAmount && (
              <div className="flex justify-between text-sm text-blue-800">
                <span>You will receive:</span>
                <span className="font-medium">
                  {bridgeState.receiveAmount} CRYPTO on {direction === 'sol-to-eth' ? 'Ethereum' : 'Solana'}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {bridgeState.error && (
          <div className="mb-4 p-3 bg-red-50 rounded-lg">
            <p className="text-sm text-red-800">{bridgeState.error}</p>
          </div>
        )}

        {/* Success Display */}
        {bridgeState.txHash && (
          <div className="mb-4 p-3 bg-green-50 rounded-lg space-y-2">
            <p className="text-sm text-green-800">
              Transaction sent! Hash: {bridgeState.txHash.slice(0, 8)}...
            </p>
            {bridgeState.layerZeroScanLink && (
              <a
                href={bridgeState.layerZeroScanLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 underline block"
              >
                View on LayerZero Scan ↗
              </a>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2">
          {direction === 'sol-to-eth' && (
            <>
              <button
                onClick={quoteSolanaToEthereum}
                disabled={!canQuote || bridgeState.isLoading}
                className="w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
              >
                {bridgeState.isLoading ? 'Getting Quote...' : 'Get Quote'}
              </button>

              <button
                onClick={executeSolanaToEthereum}
                disabled={!canExecute || bridgeState.isLoading}
                className="w-full py-2 px-4 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 transition-colors"
              >
                {bridgeState.isLoading ? 'Sending...' : 'Send Transaction'}
              </button>
            </>
          )}

          {direction === 'eth-to-sol' && (
            <>
              <button
                onClick={quoteEthereumToSolana}
                disabled={!canQuote || bridgeState.isLoading}
                className="w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
              >
                {bridgeState.isLoading ? 'Getting Quote...' : 'Get Quote'}
              </button>

              <button
                onClick={executeEthereumToSolana}
                disabled={!canExecute || bridgeState.isLoading}
                className="w-full py-2 px-4 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 transition-colors"
              >
                {bridgeState.isLoading ? 'Sending...' : 'Send Transaction'}
              </button>
            </>
          )}
        </div>
      </div>

      <TransactionStatus
        transactions={transactions}
        onClearHistory={clearTransactionHistory}
      />
    </div>
  );
}
