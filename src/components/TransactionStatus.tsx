"use client";
import { useState, useEffect } from "react";

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

interface TransactionStatusProps {
  transactions: Transaction[];
  onClearHistory: () => void;
}

export default function TransactionStatus({ transactions, onClearHistory }: TransactionStatusProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'confirmed':
        return 'text-green-600 bg-green-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getChainIcon = (chain: 'solana' | 'ethereum') => {
    return chain === 'solana' ? '◎' : 'Ξ';
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getExplorerLink = (hash: string, chain: 'solana' | 'ethereum') => {
    if (chain === 'solana') {
      return `https://explorer.solana.com/tx/${hash}`;
    } else {
      return `https://etherscan.io/tx/${hash}`;
    }
  };

  if (transactions.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
        <h3 className="text-lg font-semibold mb-4">Transaction History</h3>
        <p className="text-gray-500 text-center py-8">No transactions yet</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Transaction History</h3>
        <button
          onClick={onClearHistory}
          className="text-sm text-red-600 hover:text-red-800 transition-colors"
        >
          Clear History
        </button>
      </div>

      <div className="space-y-3">
        {transactions.map((tx, index) => (
          <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getChainIcon(tx.fromChain)}</span>
                <span className="text-gray-400">→</span>
                <span className="text-lg">{getChainIcon(tx.toChain)}</span>
                <span className="font-medium">{tx.amount}</span>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tx.status)}`}>
                {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
              </span>
            </div>

            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{formatTimestamp(tx.timestamp)}</span>
              <div className="flex space-x-2">
                <a
                  href={getExplorerLink(tx.hash, tx.fromChain)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Explorer ↗
                </a>
                {tx.layerZeroScanLink && (
                  <a
                    href={tx.layerZeroScanLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-purple-600 hover:text-purple-800 transition-colors"
                  >
                    LayerZero ↗
                  </a>
                )}
              </div>
            </div>

            <div className="mt-2 text-xs text-gray-500 font-mono">
              {tx.hash.slice(0, 8)}...{tx.hash.slice(-8)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
