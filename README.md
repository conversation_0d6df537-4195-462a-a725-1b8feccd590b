# LayerZero Bridge - Mainnet Solana ↔ Ethereum

A Next.js application for bridging tokens between Solana and Ethereum mainnet using LayerZero's OFT (Omnichain Fungible Token) protocol.

## Features

- **Mainnet Support**: Configured for Solana and Ethereum mainnet networks
- **Bidirectional Bridging**: Support for Solana → Ethereum (implemented) and Ethereum → Solana (coming soon)
- **Wallet Integration**:
  - Solana: Phantom and other Solana wallets via wallet-adapter
  - Ethereum: MetaMask and WalletConnect via wagmi
- **Real-time Quotes**: Get accurate bridge fees before executing transactions
- **Transaction History**: Track your bridge transactions with status updates
- **Responsive UI**: Modern, mobile-friendly interface

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.local.example .env.local
```

Update `.env.local` with your configuration:

```env
# Solana Mainnet Configuration
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS=4ikwYoNvoGEwtMbziUyYBTz1zRM6nmxspsfw9G7Bpump
NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS=HJh7e4FDaNkv24HTQr2wpSyrK9mQXPgCoiQsFd4CcmKF
NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS=8zPmKwY3Wv9yVoP6xZnzkAUJX5BeqM7aVMDZio4PBDyb
NEXT_PUBLIC_SOLANA_OFT_STORE_ADDRESS=FH584f6ycZcHeZGqJvACirm8gztXkJPbuBQMC6Y4Qfdb

# Ethereum Mainnet Configuration
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS=******************************************

# WalletConnect Project ID (get from https://cloud.walletconnect.com/)
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id_here

# Default bridge amount
NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT=0.1
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## OFT Configuration

### Solana OFT Details
- **Program ID**: `8zPmKwY3Wv9yVoP6xZnzkAUJX5BeqM7aVMDZio4PBDyb`
- **Mint Address**: `4ikwYoNvoGEwtMbziUyYBTz1zRM6nmxspsfw9G7Bpump`
- **Escrow Address**: `HJh7e4FDaNkv24HTQr2wpSyrK9mQXPgCoiQsFd4CcmKF`
- **OFT Store**: `FH584f6ycZcHeZGqJvACirm8gztXkJPbuBQMC6Y4Qfdb`

### Ethereum OFT Details
- **Contract Address**: `******************************************`

## Usage

1. **Connect Wallets**: Connect both your Solana and Ethereum wallets
2. **Select Direction**: Choose Solana → Ethereum (Ethereum → Solana coming soon)
3. **Enter Amount**: Specify the amount of tokens to bridge
4. **Get Quote**: Click "Get Quote" to see the estimated bridge fee
5. **Execute Transaction**: Click "Send Transaction" to execute the bridge
6. **Track Progress**: Monitor your transaction in the history section

## Architecture

### Components

- **`BridgeInterface`**: Main bridge functionality with quote and execution
- **`SolanaConnect`**: Solana wallet connection component
- **`EthereumConnect`**: Ethereum wallet connection with wagmi
- **`TransactionStatus`**: Transaction history and status tracking
- **`OftQuote`**: Legacy quote component (for reference)

### Key Dependencies

- **LayerZero**: `@layerzerolabs/oft-v2-solana-sdk`, `@layerzerolabs/lz-definitions`
- **Solana**: `@solana/wallet-adapter-*`, `@metaplex-foundation/umi`
- **Ethereum**: `wagmi`, `viem`, `ethers`
- **UI**: `Next.js 15`, `TailwindCSS`

## Development Notes

- The application is configured for **mainnet only**
- Solana → Ethereum bridging is fully implemented
- Ethereum → Solana bridging is planned for future implementation
- All environment variables must be properly configured for the bridge to function
- Make sure to have sufficient SOL for transaction fees when bridging from Solana

## Security Considerations

- Always verify contract addresses before bridging
- Start with small amounts for testing
- Keep your private keys secure
- Verify transactions on block explorers before considering them final

## Support

For issues related to:
- **LayerZero Protocol**: Check [LayerZero Documentation](https://docs.layerzero.network/)
- **Solana Integration**: See [Solana Documentation](https://docs.solana.com/)
- **Ethereum Integration**: See [wagmi Documentation](https://wagmi.sh/)

## License

This project is for educational and development purposes. Please review and understand the code before using with real funds.
