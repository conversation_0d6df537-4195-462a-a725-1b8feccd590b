{"name": "lz-solana-oft-sdk-next-example", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "overrides": {"@solana/web3.js": "^1.98.0"}, "resolutions": {"@solana/web3.js": "^1.98.0"}, "pnpm": {"overrides": {"@solana/web3.js": "^1.98.0"}}, "dependencies": {"@layerzerolabs/lz-definitions": "^3.0.71", "@layerzerolabs/lz-v2-utilities": "^3.0.71", "@layerzerolabs/oft-v2-solana-sdk": "^3.0.71", "@metaplex-foundation/mpl-toolbox": "^0.10.0", "@metaplex-foundation/umi": "^0.9.2", "@metaplex-foundation/umi-bundle-defaults": "^0.9.2", "@metaplex-foundation/umi-signer-wallet-adapters": "^0.9.2", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.24", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.98.0", "@tanstack/react-query": "^5.80.6", "@wagmi/connectors": "^5.8.5", "@wagmi/core": "^2.17.3", "bs58": "^6.0.0", "ethers": "^6.14.3", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.31.0", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}